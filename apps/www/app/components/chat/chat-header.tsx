import * as React from 'react';
import { Plus } from 'lucide-react';
import { But<PERSON> } from '~/components/ui/button';
import { useChatActions } from '~/hooks/useChatActions';

export function ChatHeader() {
  const { createNewThread } = useChatActions();

  return (
    <header className="h-14 border-b border-border px-6 flex items-center justify-between">
      <div className="flex items-center gap-3">
        {/* The back button was here */}
        <h1 className="font-semibold text-sm">Chat IA</h1>
      </div>
      <div className="flex items-center gap-2">
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8"
          onClick={createNewThread}
        >
          <Plus className="h-4 w-4" />
        </Button>
        {/* The settings button was here */}
      </div>
    </header>
  );
}
