import { Injectable, OnM<PERSON>uleD<PERSON>roy, OnModuleInit, Logger } from '@nestjs/common';
import { createClient, RedisClientType } from 'redis';

@Injectable()
export class RedisService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(RedisService.name);
  private client: RedisClientType;

  getClient(): RedisClientType {
    return this.client;
  }

  async onModuleInit() {
    this.client = createClient({
      url: process.env.REDIS_URL,
      // Production optimizations
      socket: {
        connectTimeout: 5000, // 5 seconds
        commandTimeout: 3000, // 3 seconds
        lazyConnect: true,
        reconnectStrategy: (retries) => {
          if (retries > 3) {
            this.logger.error('Redis connection failed after 3 retries');
            return false; // Stop retrying
          }
          return Math.min(retries * 100, 3000); // Exponential backoff, max 3s
        },
      },
      // Connection pool settings
      isolationPoolOptions: {
        min: 2,
        max: 10,
      },
    });

    // Error handling
    this.client.on('error', (err) => {
      this.logger.error('Redis Client Error:', err);
    });

    this.client.on('connect', () => {
      this.logger.log('Redis Client Connected');
    });

    this.client.on('ready', () => {
      this.logger.log('Redis Client Ready');
    });

    await this.client.connect();
  }

  async onModuleDestroy() {
    await this.client.disconnect();
  }
}
