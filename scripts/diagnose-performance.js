#!/usr/bin/env node

/**
 * Performance Diagnostic Script for Railway Production
 * 
 * This script helps diagnose performance issues in production by:
 * 1. Testing database connection speed
 * 2. Testing Redis connection speed
 * 3. Checking connection pooling configuration
 * 4. Measuring network latency
 */

const { PrismaClient } = require('@prisma/client');
const { createClient } = require('redis');

async function measureTime(label, fn) {
  const start = Date.now();
  try {
    const result = await fn();
    const duration = Date.now() - start;
    console.log(`✅ ${label}: ${duration}ms`);
    return { success: true, duration, result };
  } catch (error) {
    const duration = Date.now() - start;
    console.log(`❌ ${label}: ${duration}ms (ERROR: ${error.message})`);
    return { success: false, duration, error: error.message };
  }
}

async function diagnoseDatabasePerformance() {
  console.log('\n🔍 Database Performance Diagnostics');
  console.log('=====================================');
  
  const prisma = new PrismaClient({
    log: ['query', 'info', 'warn', 'error'],
  });

  // Test basic connection
  await measureTime('Database Connection', async () => {
    await prisma.$connect();
    return 'Connected';
  });

  // Test simple query
  await measureTime('Simple Query (SELECT 1)', async () => {
    return await prisma.$queryRaw`SELECT 1 as test`;
  });

  // Test complex query (if users table exists)
  await measureTime('Complex Query (User Count)', async () => {
    return await prisma.user.count();
  });

  // Test multiple concurrent queries
  await measureTime('5 Concurrent Queries', async () => {
    const promises = Array(5).fill().map(() => 
      prisma.$queryRaw`SELECT 1 as test`
    );
    return await Promise.all(promises);
  });

  await prisma.$disconnect();
}

async function diagnoseRedisPerformance() {
  console.log('\n🔍 Redis Performance Diagnostics');
  console.log('==================================');
  
  const redis = createClient({
    url: process.env.REDIS_URL,
    socket: {
      connectTimeout: 5000,
      commandTimeout: 3000,
    },
  });

  // Test connection
  await measureTime('Redis Connection', async () => {
    await redis.connect();
    return 'Connected';
  });

  // Test ping
  await measureTime('Redis Ping', async () => {
    return await redis.ping();
  });

  // Test set/get
  await measureTime('Redis SET/GET', async () => {
    await redis.set('test:performance', 'test-value', { EX: 60 });
    return await redis.get('test:performance');
  });

  // Test multiple operations
  await measureTime('5 Concurrent Redis Operations', async () => {
    const promises = Array(5).fill().map((_, i) => 
      redis.set(`test:perf:${i}`, `value-${i}`, { EX: 60 })
    );
    return await Promise.all(promises);
  });

  await redis.disconnect();
}

function diagnoseEnvironment() {
  console.log('\n🔍 Environment Diagnostics');
  console.log('============================');
  
  console.log(`Node.js Version: ${process.version}`);
  console.log(`Platform: ${process.platform}`);
  console.log(`Architecture: ${process.arch}`);
  console.log(`Memory Usage: ${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)}MB`);
  
  // Check environment variables
  const envVars = [
    'NODE_ENV',
    'DATABASE_URL',
    'REDIS_URL',
    'RAILWAY_ENVIRONMENT',
    'RAILWAY_REGION',
    'NODE_OPTIONS'
  ];
  
  console.log('\nEnvironment Variables:');
  envVars.forEach(varName => {
    const value = process.env[varName];
    if (varName.includes('URL') && value) {
      // Mask sensitive URLs
      const masked = value.replace(/:\/\/[^@]+@/, '://***:***@');
      console.log(`  ${varName}: ${masked}`);
    } else {
      console.log(`  ${varName}: ${value || 'NOT SET'}`);
    }
  });

  // Check DATABASE_URL for connection pooling
  const dbUrl = process.env.DATABASE_URL;
  if (dbUrl) {
    const hasPooling = dbUrl.includes('connection_limit') || 
                      dbUrl.includes('pool_timeout') || 
                      dbUrl.includes('pgbouncer');
    console.log(`\n🔧 Connection Pooling: ${hasPooling ? '✅ ENABLED' : '❌ MISSING'}`);
    
    if (!hasPooling) {
      console.log('⚠️  WARNING: DATABASE_URL missing connection pooling parameters!');
      console.log('   Add: ?connection_limit=20&pool_timeout=20&connect_timeout=10');
    }
  }
}

async function main() {
  console.log('🚀 Railway Production Performance Diagnostics');
  console.log('==============================================');
  console.log(`Timestamp: ${new Date().toISOString()}`);
  
  try {
    // Environment check
    diagnoseEnvironment();
    
    // Database performance
    await diagnoseDatabasePerformance();
    
    // Redis performance
    await diagnoseRedisPerformance();
    
    console.log('\n✅ Diagnostics Complete!');
    console.log('\nNext Steps:');
    console.log('1. If database queries are slow (>50ms), add connection pooling to DATABASE_URL');
    console.log('2. If Redis is slow (>10ms), check Redis region and memory usage');
    console.log('3. If both are slow, check Railway service regions');
    
  } catch (error) {
    console.error('\n❌ Diagnostic Error:', error.message);
    process.exit(1);
  }
}

// Run diagnostics
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { diagnoseDatabasePerformance, diagnoseRedisPerformance, diagnoseEnvironment };
