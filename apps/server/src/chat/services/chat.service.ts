// apps/server/src/chat/services/chat.service.ts
import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { OpenRouterService } from './openrouter.service';
import { GeminiService } from './gemini.service';
import { PrismaService } from '../../prisma/prisma.service';
import { RedisService } from '../../redis/redis.service';
import { v4 as uuidv4 } from 'uuid';
import { Message } from '@prisma/client';
import {
  ChatMessageResponseDto,
  CreateChatMessageDto,
} from '../dto/chat-message.dto';

// Define the discriminated union for stream events
type StreamEvent =
  | { type: 'chunk'; payload: string }
  | { type: 'final'; payload: ChatMessageResponseDto };

// Cache configuration
const CACHE_TTL = {
  CONVERSATION_HISTORY: 300, // 5 minutes
} as const;

const CACHE_KEYS = {
  CONVERSATION_HISTORY: (threadId: string) => `chat:history:${threadId}`,
} as const;

@Injectable()
export class ChatService {
  private readonly logger = new Logger(ChatService.name);

  constructor(
    private openRouterService: OpenRouterService,
    private geminiService: GeminiService,
    private prisma: PrismaService,
    private redis: RedisService,
  ) {}

  // Cache management methods
  private async invalidateThreadCache(threadId: string): Promise<void> {
    try {
      const client = this.redis.getClient();
      const cacheKey = CACHE_KEYS.CONVERSATION_HISTORY(threadId);
      await client.del(cacheKey);
      this.logger.debug(`Invalidated cache for thread ${threadId}`);
    } catch (error) {
      this.logger.warn(
        `Cache invalidation failed for thread ${threadId}:`,
        error,
      );
    }
  }

  private async toMessageDto(
    message: Message,
  ): Promise<ChatMessageResponseDto> {
    // The sibling count should reflect all versions, including deleted ones.
    let siblingCount = 1;
    if (message.parentId) {
      siblingCount = await this.prisma.message.count({
        where: { parentId: message.parentId }, // Count all siblings
      });
    }

    const siblingPosition = message.siblingPosition ?? 1;

    return {
      id: message.id,
      content: message.content,
      role: message.role,
      model: message.model,
      threadId: message.threadId,
      createdAt: message.createdAt
        ? new Date(message.createdAt).toISOString()
        : new Date().toISOString(),
      parentId: message.parentId,
      siblingCount,
      siblingPosition,
      deletedAt: message.deletedAt
        ? new Date(message.deletedAt).toISOString()
        : null,
    };
  }

  async createMessage(
    userId: string,
    createDto: CreateChatMessageDto,
  ): Promise<ChatMessageResponseDto> {
    const { content, threadId, parentId, idempotencyKey } = createDto;

    // 1. Check for an existing message with the idempotency key
    if (idempotencyKey) {
      const existingMessage = await this.prisma.message.findUnique({
        where: { idempotencyKey },
      });
      if (existingMessage) {
        this.logger.log(
          `Idempotency key match found for key: ${idempotencyKey}. Returning existing message.`,
        );
        return this.toMessageDto(existingMessage);
      }
    }

    // 2. If no existing message, proceed with creation
    const thread = await this.getOrCreateThread(userId, content, threadId);

    if (parentId) {
      const parentMessage = await this.prisma.message.findFirst({
        where: { id: parentId, thread: { userId } },
      });
      if (!parentMessage) {
        throw new NotFoundException('Parent message not found.');
      }
    }

    const userMessage = await this.prisma.message.create({
      data: {
        id: uuidv4(),
        content,
        role: 'USER',
        threadId: thread.id,
        parentId: parentId,
      },
    });

    if (parentId) {
      await this.prisma.message.update({
        where: { id: parentId },
        data: { nextMessageId: userMessage.id },
      });
    }

    // Invalidate cache since we added a new message
    await this.invalidateThreadCache(thread.id);

    return this.toMessageDto(userMessage);
  }

  private async getOrCreateThread(
    userId: string,
    content: string,
    threadId?: string,
  ) {
    if (threadId) {
      const thread = await this.prisma.thread.findFirst({
        where: { id: threadId, userId },
      });
      if (thread) return thread;
    }
    return this.prisma.thread.create({
      data: {
        id: uuidv4(),
        title: content.substring(0, 50),
        user: { connect: { id: userId } },
      },
    });
  }

  async *generateAIResponse(
    userId: string,
    messageParentId: string,
    model: string,
  ): AsyncGenerator<StreamEvent, void, unknown> {
    // The parent message is the one we are replying to.
    const parentMessage = await this.prisma.message.findFirstOrThrow({
      where: { id: messageParentId, thread: { userId } },
    });

    // For the AI to have context, we must fetch the entire chain of messages leading to the parent.
    const historyMessages = await this.prisma.$queryRaw<Message[]>`
      WITH RECURSIVE "MessageHierarchy" AS (
        SELECT * FROM "messages" WHERE "id" = ${parentMessage.id}
        UNION ALL
        SELECT "m".* FROM "messages" "m"
        JOIN "MessageHierarchy" "h" ON "m"."id" = "h"."parent_id"
      )
      SELECT * FROM "MessageHierarchy"
      WHERE "deleted_at" IS NULL
      ORDER BY "created_at" ASC;
    `;

    const formattedMessages = historyMessages.map((msg) => ({
      role: msg.role.toLowerCase() as 'user' | 'assistant',
      content: msg.content,
    }));

    let streamGenerator: AsyncGenerator<string, void, unknown>;
    if (model.startsWith('google/gemma') || model.startsWith('gpt')) {
      streamGenerator = this.openRouterService.streamChatCompletion({
        messages: formattedMessages,
        model,
      });
    } else if (model.startsWith('gemini')) {
      streamGenerator = this.geminiService.streamChatCompletion({
        messages: formattedMessages,
        model,
      });
    } else {
      throw new Error(`Unsupported model: ${model}`);
    }

    let fullResponse = '';
    for await (const chunk of streamGenerator) {
      fullResponse += chunk;
      // Yield each chunk as a 'chunk' type event
      yield { type: 'chunk', payload: chunk };
    }

    const maxSiblingPosition = await this.prisma.message.aggregate({
      _max: { siblingPosition: true },
      where: { parentId: parentMessage.id },
    });
    const newSiblingPosition =
      (maxSiblingPosition._max.siblingPosition ?? 0) + 1;

    const aiMessage = await this.prisma.message.create({
      data: {
        id: uuidv4(),
        content: fullResponse,
        role: 'ASSISTANT',
        threadId: parentMessage.threadId,
        model,
        parentId: parentMessage.id,
        siblingPosition: newSiblingPosition,
      },
    });

    await this.prisma.message.update({
      where: { id: parentMessage.id },
      data: { nextMessageId: aiMessage.id },
    });

    await this.prisma.thread.update({
      where: { id: parentMessage.threadId },
      data: { updatedAt: new Date() },
    });

    const finalMessageDto = await this.toMessageDto(aiMessage);
    // Yield the final, complete DTO as a 'final' type event
    yield { type: 'final', payload: finalMessageDto };
  }

  async getConversationHistory(
    userId: string,
    threadId: string,
  ): Promise<ChatMessageResponseDto[]> {
    const startTime = Date.now();
    const timings: Record<string, number> = {};

    // Try to get from cache first
    try {
      const cacheStartTime = Date.now();
      const client = this.redis.getClient();
      const cacheKey = CACHE_KEYS.CONVERSATION_HISTORY(threadId);
      const cached = await client.get(cacheKey);
      timings.cacheRead = Date.now() - cacheStartTime;

      if (cached && typeof cached === 'string') {
        const result = JSON.parse(cached) as ChatMessageResponseDto[];
        this.logger.log(
          `Cache hit for thread ${threadId} - Total: ${Date.now() - startTime}ms (cache: ${timings.cacheRead}ms)`,
        );
        return result;
      }
    } catch (error) {
      timings.cacheRead = Date.now() - startTime;
      this.logger.warn(`Cache read failed for thread ${threadId} (${timings.cacheRead}ms):`, error);
    }

    // Cache miss - fetch from database with optimized query
    const dbStartTime = Date.now();
    const result = await this.getConversationHistoryFromDB(userId, threadId);
    timings.dbQuery = Date.now() - dbStartTime;

    // Cache the result
    const cacheWriteStartTime = Date.now();
    try {
      const client = this.redis.getClient();
      const cacheKey = CACHE_KEYS.CONVERSATION_HISTORY(threadId);
      await client.setEx(
        cacheKey,
        CACHE_TTL.CONVERSATION_HISTORY,
        JSON.stringify(result),
      );
      timings.cacheWrite = Date.now() - cacheWriteStartTime;
    } catch (error) {
      timings.cacheWrite = Date.now() - cacheWriteStartTime;
      this.logger.warn(`Cache write failed for thread ${threadId} (${timings.cacheWrite}ms):`, error);
    }

    const totalTime = Date.now() - startTime;
    this.logger.log(
      `DB fetch for thread ${threadId} - Total: ${totalTime}ms (cache: ${timings.cacheRead || 0}ms, db: ${timings.dbQuery}ms, cache-write: ${timings.cacheWrite || 0}ms)`,
    );
    return result;
  }

  private async getConversationHistoryFromDB(
    userId: string,
    threadId: string,
  ): Promise<ChatMessageResponseDto[]> {
    try {
      // Get the root message
      const rootMessages = await this.prisma.message.findMany({
        where: { threadId, thread: { userId }, parentId: null },
        orderBy: { createdAt: 'asc' },
      });

      if (!rootMessages.length) return [];

      const rootNode = rootMessages[0];
      const startNodeId =
        rootNode.role === 'SYSTEM' && rootNode.nextMessageId
          ? rootNode.nextMessageId
          : rootNode.id;

      // Fallback to the original working approach for now
      const conversationPath: Message[] = [];
      let currentMessage: Message | null = await this.prisma.message.findUnique(
        {
          where: { id: startNodeId },
        },
      );

      while (currentMessage) {
        conversationPath.push(currentMessage);
        if (!currentMessage.nextMessageId) break;
        currentMessage = await this.prisma.message.findUnique({
          where: { id: currentMessage.nextMessageId },
        });
      }

      // Convert to DTOs using the original working method
      return Promise.all(conversationPath.map((msg) => this.toMessageDto(msg)));
    } catch (error) {
      this.logger.error(
        `Failed to fetch conversation history from DB for thread ${threadId}:`,
        error,
      );
      throw error;
    }
  }

  async editMessage(
    userId: string,
    messageId: string,
    newContent: string,
  ): Promise<ChatMessageResponseDto[]> {
    const messageToEdit = await this.prisma.message.findFirstOrThrow({
      where: { id: messageId, thread: { userId } },
    });

    let effectiveParentId = messageToEdit.parentId;
    let parentToUpdate: { id: string };

    if (!effectiveParentId) {
      this.logger.log(
        `First edit on root message ${messageId}, creating container.`,
      );

      const containerParent = await this.prisma.message.create({
        data: {
          id: uuidv4(),
          content: `Container for root versions in thread ${messageToEdit.threadId}`,
          role: 'SYSTEM',
          threadId: messageToEdit.threadId,
        },
      });

      await this.prisma.message.update({
        where: { id: messageToEdit.id },
        data: {
          parentId: containerParent.id,
          siblingPosition: 1,
        },
      });

      effectiveParentId = containerParent.id;
      parentToUpdate = containerParent;
    } else {
      const parentMessage = await this.prisma.message.findUnique({
        where: { id: effectiveParentId },
      });
      if (!parentMessage) {
        throw new NotFoundException('Parent message not found');
      }
      parentToUpdate = parentMessage;
    }

    this.logger.log(`Creating new sibling for message ${messageId}.`);

    const maxSiblingPosition = await this.prisma.message.aggregate({
      _max: { siblingPosition: true },
      where: { parentId: effectiveParentId },
    });
    const newSiblingPosition =
      (maxSiblingPosition._max.siblingPosition ?? 0) + 1;

    const newSiblingMessage = await this.prisma.message.create({
      data: {
        id: uuidv4(),
        content: newContent,
        role: messageToEdit.role,
        threadId: messageToEdit.threadId,
        model: messageToEdit.model,
        parentId: effectiveParentId,
        siblingPosition: newSiblingPosition,
      },
    });

    await this.prisma.message.update({
      where: { id: parentToUpdate.id },
      data: { nextMessageId: newSiblingMessage.id },
    });

    // Invalidate cache since we edited a message
    await this.invalidateThreadCache(messageToEdit.threadId);

    return this.getConversationHistory(userId, messageToEdit.threadId);
  }

  async deleteMessage(
    userId: string,
    messageId: string,
  ): Promise<ChatMessageResponseDto[]> {
    const messageToDelete = await this.prisma.message.findFirstOrThrow({
      where: { id: messageId, thread: { userId } },
    });

    let effectiveParentId = messageToDelete.parentId;
    let parentToUpdate: { id: string };

    // Handle branching from a root message
    if (!effectiveParentId) {
      this.logger.log(`Branching from root message ${messageId} for deletion.`);
      const containerParent = await this.prisma.message.create({
        data: {
          id: uuidv4(),
          content: `Container for root versions in thread ${messageToDelete.threadId}`,
          role: 'SYSTEM',
          threadId: messageToDelete.threadId,
        },
      });

      await this.prisma.message.update({
        where: { id: messageToDelete.id },
        data: { parentId: containerParent.id, siblingPosition: 1 },
      });

      effectiveParentId = containerParent.id;
      parentToUpdate = containerParent;
    } else {
      const parentMessage = await this.prisma.message.findUnique({
        where: { id: effectiveParentId },
      });
      if (!parentMessage) {
        throw new NotFoundException('Parent message not found');
      }
      parentToUpdate = parentMessage;
    }

    this.logger.log(`Creating new 'deleted' sibling for message ${messageId}.`);

    const maxSiblingPosition = await this.prisma.message.aggregate({
      _max: { siblingPosition: true },
      where: { parentId: effectiveParentId },
    });
    const newSiblingPosition =
      (maxSiblingPosition._max.siblingPosition ?? 0) + 1;

    // Create the new sibling, marked as deleted
    const newDeletedSibling = await this.prisma.message.create({
      data: {
        id: uuidv4(),
        content: messageToDelete.content,
        role: messageToDelete.role,
        threadId: messageToDelete.threadId,
        model: messageToDelete.model,
        parentId: effectiveParentId,
        siblingPosition: newSiblingPosition,
        deletedAt: new Date(), // Soft delete this new node
      },
    });

    // Point the parent to this new branch
    await this.prisma.message.update({
      where: { id: parentToUpdate.id },
      data: { nextMessageId: newDeletedSibling.id },
    });

    // Invalidate cache since we deleted a message
    await this.invalidateThreadCache(messageToDelete.threadId);

    return this.getConversationHistory(userId, messageToDelete.threadId);
  }

  async navigateSiblings(
    userId: string,
    messageId: string,
    direction: 'next' | 'prev',
  ): Promise<ChatMessageResponseDto[]> {
    const startTime = Date.now();
    const timings: Record<string, number> = {};

    // Perform navigation (no caching for navigation results to avoid state bugs)
    const dbStartTime = Date.now();
    const currentMessage = await this.prisma.message.findFirstOrThrow({
      where: { id: messageId, thread: { userId } },
    });
    timings.findCurrentMessage = Date.now() - dbStartTime;

    if (!currentMessage.parentId) {
      throw new NotFoundException(
        'This message has no siblings to navigate to.',
      );
    }

    const newPosition =
      direction === 'next'
        ? currentMessage.siblingPosition + 1
        : currentMessage.siblingPosition - 1;

    const siblingStartTime = Date.now();
    const newSibling = await this.prisma.message.findFirst({
      where: {
        parentId: currentMessage.parentId,
        siblingPosition: newPosition,
      },
    });
    timings.findSibling = Date.now() - siblingStartTime;

    if (!newSibling) {
      throw new NotFoundException(
        'No sibling found in the specified direction.',
      );
    }

    // Update the parent pointer
    const updateStartTime = Date.now();
    await this.prisma.message.update({
      where: { id: currentMessage.parentId },
      data: { nextMessageId: newSibling.id },
    });
    timings.updateParent = Date.now() - updateStartTime;

    // Invalidate conversation cache since we changed the active path
    const cacheInvalidateStartTime = Date.now();
    await this.invalidateThreadCache(currentMessage.threadId);
    timings.cacheInvalidate = Date.now() - cacheInvalidateStartTime;

    // Get the updated conversation history
    const historyStartTime = Date.now();
    const result = await this.getConversationHistory(
      userId,
      currentMessage.threadId,
    );
    timings.getHistory = Date.now() - historyStartTime;

    const totalTime = Date.now() - startTime;
    this.logger.log(
      `Navigation ${direction} for message ${messageId} - Total: ${totalTime}ms (find-current: ${timings.findCurrentMessage}ms, find-sibling: ${timings.findSibling}ms, update: ${timings.updateParent}ms, cache-invalidate: ${timings.cacheInvalidate}ms, get-history: ${timings.getHistory}ms)`,
    );

    // Note: Not caching navigation results to avoid state inconsistencies

    this.logger.debug(
      `Navigation completed for ${messageId}:${direction} (${Date.now() - startTime}ms)`,
    );
    return result;
  }

  async getUserThreads(userId: string) {
    return this.prisma.thread.findMany({
      where: {
        userId,
        messages: { some: { parentId: null } },
      },
      orderBy: { updatedAt: 'desc' },
    });
  }

  async createThread(userId: string, title: string) {
    return this.prisma.thread.create({
      data: {
        id: uuidv4(),
        title: title.substring(0, 50),
        user: { connect: { id: userId } },
      },
    });
  }
}
