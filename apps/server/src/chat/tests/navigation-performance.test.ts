// Performance test for navigation optimization
import { Test, TestingModule } from '@nestjs/testing';
import { ChatService } from '../services/chat.service';
import { PrismaService } from '../../prisma/prisma.service';
import { RedisService } from '../../redis/redis.service';
import { OpenRouterService } from '../services/openrouter.service';
import { GeminiService } from '../services/gemini.service';
import { ConfigService } from '@nestjs/config';

describe('Navigation Performance Tests', () => {
  let chatService: ChatService;
  let prismaService: PrismaService;
  let redisService: RedisService;
  let module: TestingModule;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      providers: [
        ChatService,
        {
          provide: PrismaService,
          useValue: {
            message: {
              findFirstOrThrow: jest.fn(),
              findFirst: jest.fn(),
              update: jest.fn(),
              findMany: jest.fn(),
              groupBy: jest.fn(),
            },
            $queryRaw: jest.fn(),
          },
        },
        {
          provide: RedisService,
          useValue: {
            getClient: jest.fn().mockReturnValue({
              get: jest.fn(),
              setEx: jest.fn(),
              del: jest.fn(),
              scan: jest.fn().mockResolvedValue({ cursor: '0', keys: [] }),
            }),
          },
        },
        {
          provide: OpenRouterService,
          useValue: {},
        },
        {
          provide: GeminiService,
          useValue: {},
        },
        {
          provide: ConfigService,
          useValue: {},
        },
      ],
    }).compile();

    chatService = module.get<ChatService>(ChatService);
    prismaService = module.get<PrismaService>(PrismaService);
    redisService = module.get<RedisService>(RedisService);
  });

  afterAll(async () => {
    await module.close();
  });

  describe('Navigation Performance', () => {
    const mockUserId = 'test-user-id';
    const mockThreadId = 'test-thread-id';
    const mockMessageId = 'test-message-id';
    const mockCurrentMessage = {
      id: mockMessageId,
      threadId: mockThreadId,
      parentId: 'parent-id',
      siblingPosition: 1,
      content: 'Test message',
      role: 'USER' as const,
      createdAt: new Date(),
      deletedAt: null,
      model: null,
      tokens: 0,
      idempotencyKey: null,
      nextMessageId: null,
    };

    const mockSibling = {
      ...mockCurrentMessage,
      id: 'sibling-id',
      siblingPosition: 2,
    };

    const mockConversationPath = [mockCurrentMessage, mockSibling];

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should complete navigation in under 50ms with cache hit', async () => {
      // Mock cache hit
      const redisClient = redisService.getClient();
      (redisClient.get as jest.Mock).mockResolvedValueOnce(
        JSON.stringify([
          {
            id: mockMessageId,
            content: 'Test message',
            role: 'USER',
            createdAt: new Date().toISOString(),
            threadId: mockThreadId,
            parentId: 'parent-id',
            siblingCount: 2,
            siblingPosition: 2,
            deletedAt: null,
          },
        ]),
      );

      const startTime = Date.now();

      await chatService.navigateSiblings(mockUserId, mockMessageId, 'next');

      const duration = Date.now() - startTime;

      expect(duration).toBeLessThan(50);
      expect(redisClient.get).toHaveBeenCalled();
    });

    it('should complete navigation in under 200ms with cache miss', async () => {
      // Mock cache miss
      const redisClient = redisService.getClient();
      (redisClient.get as jest.Mock).mockResolvedValue(null);
      (redisClient.setEx as jest.Mock).mockResolvedValue('OK');

      // Mock database queries
      (prismaService.message.findFirstOrThrow as jest.Mock).mockResolvedValue(
        mockCurrentMessage,
      );
      (prismaService.message.findFirst as jest.Mock).mockResolvedValue(
        mockSibling,
      );
      (prismaService.message.update as jest.Mock).mockResolvedValue({});
      (prismaService.message.findMany as jest.Mock).mockResolvedValue([
        mockCurrentMessage,
      ]);
      (prismaService.$queryRaw as jest.Mock).mockResolvedValue(
        mockConversationPath,
      );
      (prismaService.message.groupBy as jest.Mock).mockResolvedValue([
        { parentId: 'parent-id', _count: { id: 2 } },
      ]);

      const startTime = Date.now();

      await chatService.navigateSiblings(mockUserId, mockMessageId, 'next');

      const duration = Date.now() - startTime;

      expect(duration).toBeLessThan(200);
      expect(redisClient.setEx).toHaveBeenCalled();
    });

    it('should have faster subsequent calls due to caching', async () => {
      const redisClient = redisService.getClient();

      // First call - cache miss
      (redisClient.get as jest.Mock).mockResolvedValueOnce(null);
      (redisClient.setEx as jest.Mock).mockResolvedValue('OK');
      (prismaService.message.findFirstOrThrow as jest.Mock).mockResolvedValue(
        mockCurrentMessage,
      );
      (prismaService.message.findFirst as jest.Mock).mockResolvedValue(
        mockSibling,
      );
      (prismaService.message.update as jest.Mock).mockResolvedValue({});
      (prismaService.message.findMany as jest.Mock).mockResolvedValue([
        mockCurrentMessage,
      ]);
      (prismaService.$queryRaw as jest.Mock).mockResolvedValue(
        mockConversationPath,
      );
      (prismaService.message.groupBy as jest.Mock).mockResolvedValue([]);

      const firstCallStart = Date.now();
      await chatService.navigateSiblings(mockUserId, mockMessageId, 'next');
      const firstCallDuration = Date.now() - firstCallStart;

      // Second call - cache hit
      (redisClient.get as jest.Mock).mockResolvedValueOnce(
        JSON.stringify([
          {
            id: mockMessageId,
            content: 'Test message',
            role: 'USER',
            createdAt: new Date().toISOString(),
            threadId: mockThreadId,
            parentId: 'parent-id',
            siblingCount: 2,
            siblingPosition: 2,
            deletedAt: null,
          },
        ]),
      );

      const secondCallStart = Date.now();
      await chatService.navigateSiblings(mockUserId, mockMessageId, 'next');
      const secondCallDuration = Date.now() - secondCallStart;

      expect(secondCallDuration).toBeLessThan(firstCallDuration);
      expect(secondCallDuration).toBeLessThan(50);
    });
  });

  describe('Conversation History Performance', () => {
    it('should complete history fetch in under 50ms with cache hit', async () => {
      const redisClient = redisService.getClient();
      (redisClient.get as jest.Mock).mockResolvedValueOnce(
        JSON.stringify([
          {
            id: 'msg-1',
            content: 'Test message',
            role: 'USER',
            createdAt: new Date().toISOString(),
            threadId: 'thread-1',
            parentId: null,
            siblingCount: 1,
            siblingPosition: 1,
            deletedAt: null,
          },
        ]),
      );

      const startTime = Date.now();

      await chatService.getConversationHistory('user-1', 'thread-1');

      const duration = Date.now() - startTime;

      expect(duration).toBeLessThan(50);
      expect(redisClient.get).toHaveBeenCalled();
    });
  });
});
