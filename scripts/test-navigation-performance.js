#!/usr/bin/env node

/**
 * Performance testing script for navigation optimization
 * Run this script to test the actual performance of the /api/navigate endpoint
 */

const axios = require('axios');

const API_BASE = process.env.API_BASE || 'http://localhost:3001';
const TEST_USER_TOKEN = process.env.TEST_USER_TOKEN;

if (!TEST_USER_TOKEN) {
  console.error('Please set TEST_USER_TOKEN environment variable');
  process.exit(1);
}

const apiClient = axios.create({
  baseURL: API_BASE,
  headers: {
    'Authorization': `Bearer ${TEST_USER_TOKEN}`,
    'Content-Type': 'application/json',
  },
});

async function measureNavigationPerformance() {
  console.log('🚀 Starting Navigation Performance Test\n');

  try {
    // First, get a list of threads to find a suitable test thread
    console.log('📋 Fetching threads...');
    const threadsResponse = await apiClient.get('/api/chat/threads');
    const threads = threadsResponse.data.data;

    if (threads.length === 0) {
      console.log('❌ No threads found. Please create some conversations first.');
      return;
    }

    const testThread = threads[0];
    console.log(`📝 Using thread: ${testThread.id} - "${testThread.title}"`);

    // Get conversation history to find messages with siblings
    console.log('\n📚 Fetching conversation history...');
    const historyStart = Date.now();
    const historyResponse = await apiClient.get(`/api/chat/history/${testThread.id}`);
    const historyTime = Date.now() - historyStart;
    const messages = historyResponse.data.data;

    console.log(`✅ History fetched in ${historyTime}ms (${messages.length} messages)`);
    if (historyResponse.data.performance) {
      console.log(`📊 Server reported: ${historyResponse.data.performance.responseTime}ms`);
    }

    // Find a message with siblings for navigation testing
    const messageWithSiblings = messages.find(msg => msg.siblingCount > 1);
    
    if (!messageWithSiblings) {
      console.log('❌ No messages with siblings found. Navigation testing requires messages with multiple versions.');
      return;
    }

    console.log(`\n🎯 Found message with ${messageWithSiblings.siblingCount} siblings (position ${messageWithSiblings.siblingPosition})`);
    console.log(`Message: "${messageWithSiblings.content.substring(0, 50)}..."`);

    // Test navigation performance
    const navigationTests = [];
    const testCount = 5;

    console.log(`\n🔄 Running ${testCount} navigation tests...`);

    for (let i = 0; i < testCount; i++) {
      const direction = messageWithSiblings.siblingPosition > 1 ? 'prev' : 'next';
      
      const navStart = Date.now();
      try {
        const navResponse = await apiClient.post('/api/chat/navigate', {
          messageId: messageWithSiblings.id,
          direction: direction,
        });
        const navTime = Date.now() - navStart;
        
        const serverTime = navResponse.data.performance?.responseTime || 'N/A';
        navigationTests.push({
          clientTime: navTime,
          serverTime: typeof serverTime === 'number' ? serverTime : null,
          success: true,
        });
        
        console.log(`  Test ${i + 1}: ${navTime}ms (server: ${serverTime}ms)`);
      } catch (error) {
        const navTime = Date.now() - navStart;
        navigationTests.push({
          clientTime: navTime,
          serverTime: null,
          success: false,
          error: error.message,
        });
        console.log(`  Test ${i + 1}: FAILED in ${navTime}ms - ${error.message}`);
      }
    }

    // Calculate statistics
    const successfulTests = navigationTests.filter(t => t.success);
    if (successfulTests.length === 0) {
      console.log('\n❌ All navigation tests failed');
      return;
    }

    const clientTimes = successfulTests.map(t => t.clientTime);
    const serverTimes = successfulTests.map(t => t.serverTime).filter(t => t !== null);

    const avgClientTime = clientTimes.reduce((a, b) => a + b, 0) / clientTimes.length;
    const minClientTime = Math.min(...clientTimes);
    const maxClientTime = Math.max(...clientTimes);

    console.log('\n📊 Performance Results:');
    console.log('========================');
    console.log(`Client-side measurements:`);
    console.log(`  Average: ${avgClientTime.toFixed(1)}ms`);
    console.log(`  Min: ${minClientTime}ms`);
    console.log(`  Max: ${maxClientTime}ms`);

    if (serverTimes.length > 0) {
      const avgServerTime = serverTimes.reduce((a, b) => a + b, 0) / serverTimes.length;
      const minServerTime = Math.min(...serverTimes);
      const maxServerTime = Math.max(...serverTimes);

      console.log(`Server-side measurements:`);
      console.log(`  Average: ${avgServerTime.toFixed(1)}ms`);
      console.log(`  Min: ${minServerTime}ms`);
      console.log(`  Max: ${maxServerTime}ms`);
    }

    // Performance assessment
    console.log('\n🎯 Performance Assessment:');
    if (avgClientTime < 50) {
      console.log('🟢 EXCELLENT: Average response time under 50ms');
    } else if (avgClientTime < 100) {
      console.log('🟡 GOOD: Average response time under 100ms');
    } else if (avgClientTime < 200) {
      console.log('🟠 ACCEPTABLE: Average response time under 200ms');
    } else {
      console.log('🔴 NEEDS IMPROVEMENT: Average response time over 200ms');
    }

    if (minClientTime < 50) {
      console.log('✅ Cache hits are performing well (sub-50ms)');
    }

    console.log(`\nSuccess rate: ${successfulTests.length}/${navigationTests.length} (${(successfulTests.length / navigationTests.length * 100).toFixed(1)}%)`);

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the test
measureNavigationPerformance().catch(console.error);
