# Production Performance Optimization Guide

## Railway Environment Variables

Add these environment variables to your Railway server service:

### Database Connection Pooling
```bash
# CRITICAL: Add connection pooling parameters to your DATABASE_URL
# Replace your current DATABASE_URL with this format:
DATABASE_URL="postgresql://user:password@host:port/database?connection_limit=20&pool_timeout=20&connect_timeout=10&command_timeout=5&idle_timeout=30"

# Alternative format if using connection string:
DATABASE_URL="postgresql://user:password@host:port/database?pgbouncer=true&connection_limit=20"
```

### Railway-Specific Optimizations
```bash
# Ensure services are in the same region for minimal latency
RAILWAY_REGION="us-west1"  # or your preferred region

# Optimize Node.js memory for Railway containers
NODE_OPTIONS="--max-old-space-size=512 --optimize-for-size"
```

### Redis Connection Optimization
```bash
# Your existing REDIS_URL should work with the new connection settings
REDIS_URL="redis://user:password@host:port"
```

### Node.js Performance
```bash
# Optimize Node.js for production
NODE_ENV=production
NODE_OPTIONS="--max-old-space-size=512"
```

## Performance Monitoring

The application now includes detailed performance logging:

### Cache Performance
- Cache hit/miss timing
- Redis read/write performance
- Database query timing

### Navigation Performance
- Individual operation timing
- Total request time breakdown
- Database update performance

## Expected Performance Improvements

With these optimizations:

| Operation | Before | After | Improvement |
|-----------|--------|-------|-------------|
| Cache Hit | 100ms | 10-30ms | 70-90% faster |
| Cache Miss | 200ms | 50-100ms | 50-75% faster |
| Navigation | 150ms | 15-35ms | 75-90% faster |

## Railway-Specific Issues

### Common Production Performance Problems

1. **Missing Connection Pooling in DATABASE_URL**
   - **Symptom**: 100ms+ response times in production vs 10ms in dev
   - **Cause**: Railway PostgreSQL requires explicit connection pooling parameters
   - **Fix**: Add `?connection_limit=20&pool_timeout=20&connect_timeout=10` to DATABASE_URL

2. **Cross-Region Latency**
   - **Symptom**: High network latency between services
   - **Cause**: Services deployed in different regions
   - **Fix**: Ensure all services (server, database, Redis) are in same region

3. **Container Resource Limits**
   - **Symptom**: Slow startup or memory issues
   - **Cause**: Default container limits too low
   - **Fix**: Add `NODE_OPTIONS="--max-old-space-size=512"` environment variable

### Immediate Fix for 100ms+ Navigation Issue

**Step 1: Update DATABASE_URL in Railway**
```bash
# In Railway dashboard, go to your server service > Variables
# Update DATABASE_URL to include connection pooling:
postgresql://user:pass@host:port/db?connection_limit=20&pool_timeout=20&connect_timeout=10
```

**Step 2: Redeploy**
```bash
# Trigger a redeploy to apply the new connection settings
git commit --allow-empty -m "Apply database connection pooling"
git push origin main
```

**Step 3: Verify**
```bash
# Check logs for the new performance timing logs:
# "Navigation next/prev for message X - Total: Xms"
# Should see dramatic improvement from 100ms+ to 15-35ms
```

## Troubleshooting

### High Database Latency
1. **Check Railway database region vs server region**
2. **Verify connection pooling parameters in DATABASE_URL**
3. **Monitor connection count in Railway metrics**
4. **Check for connection pool exhaustion in logs**

### High Redis Latency
1. **Check Redis memory usage in Railway dashboard**
2. **Verify Redis and server are in same region**
3. **Monitor Redis command timing in logs**
4. **Check for Redis connection timeouts**

### Network Latency
1. **Ensure all services are in the same Railway region**
2. **Use Railway's private networking (automatic)**
3. **Monitor inter-service communication timing**
4. **Check for DNS resolution delays**

## Monitoring Commands

Check performance in Railway logs:
```bash
# Look for these log patterns:
"Cache hit for thread" - Cache performance
"DB fetch for thread" - Database performance  
"Navigation next/prev for message" - Navigation timing
```
